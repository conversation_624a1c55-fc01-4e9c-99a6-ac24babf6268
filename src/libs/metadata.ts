import { Metadata } from 'next';

// SEO and metadata configuration
export const siteConfig = {
  name: 'Prompt Rank | Discover, Rate, and Share the Best AI Prompts',
  description:
    'Discover, rate, and share the best AI prompts. Browse and discover trending prompts, share your best prompts with the community, and manage your account and view your contributions.',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'https://promptrank.com',
  ogImage: '/og-image.jpg',
  creator: '@promptrank',
  keywords: [
    'AI prompts',
    'prompt engineering',
    'ChatGPT prompts',
    'AI tools',
    'productivity',
    'artificial intelligence',
    'prompt sharing',
    'prompt ranking'
  ]
};

export const metadata: Metadata = {
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`
  },
  description: siteConfig.description,
  keywords: siteConfig.keywords,
  authors: [
    {
      name: siteConfig.name,
      url: siteConfig.url
    }
  ],
  creator: siteConfig.creator,
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: siteConfig.url,
    title: siteConfig.name,
    description: siteConfig.description,
    siteName: siteConfig.name,
    images: [
      {
        url: siteConfig.ogImage,
        width: 1200,
        height: 630,
        alt: siteConfig.name
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: siteConfig.name,
    description: siteConfig.description,
    images: [siteConfig.ogImage],
    creator: siteConfig.creator
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png'
  },
  manifest: '/site.webmanifest',
  metadataBase: new URL(siteConfig.url),
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1
    }
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
    yahoo: process.env.YAHOO_VERIFICATION
  }
};
