/**
 * Theme initialization script for preventing FOUC (Flash of Unstyled Content)
 * This script must be inlined and executed before any content renders
 */

export const THEME_SCRIPT = `
(function() {
  'use strict';
  
  // Configuration constants
  const CONFIG = {
    STORAGE_KEY: 'theme',
    MEDIA_QUERY: '(prefers-color-scheme: dark)',
    VALID_THEMES: ['light', 'dark', 'system'],
    FALLBACK_THEME: 'system'
  };
  
  /**
   * Safely retrieves theme from localStorage with validation
   */
  function getStoredTheme() {
    try {
      const stored = localStorage.getItem(CONFIG.STORAGE_KEY);
      return stored && CONFIG.VALID_THEMES.includes(stored) ? stored : null;
    } catch (e) {
      return null;
    }
  }
  
  /**
   * Determines system theme preference
   */
  function getSystemTheme() {
    try {
      return window.matchMedia(CONFIG.MEDIA_QUERY).matches ? 'dark' : 'light';
    } catch (e) {
      return 'light';
    }
  }
  
  /**
   * Resolves theme to actual light/dark value
   */
  function resolveTheme(theme) {
    return theme === 'system' ? getSystemTheme() : theme;
  }
  
  /**
   * Applies theme to document with minimal DOM operations
   */
  function applyTheme(theme, resolvedTheme) {
    const root = document.documentElement;
    const isDark = resolvedTheme === 'dark';
    
    // Batch DOM operations for performance
    if (isDark) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
    
    // Set color scheme for native controls
    root.style.colorScheme = resolvedTheme;
    
    // Store for React hydration
    root.setAttribute('data-theme', theme);
    root.setAttribute('data-resolved-theme', resolvedTheme);
  }
  
  /**
   * Main initialization function
   */
  function initializeTheme() {
    try {
      const storedTheme = getStoredTheme();
      const theme = storedTheme || CONFIG.FALLBACK_THEME;
      const resolvedTheme = resolveTheme(theme);
      
      applyTheme(theme, resolvedTheme);
      
    } catch (error) {
      // Fallback: apply system theme without localStorage
      console.warn('Theme initialization failed, falling back to system theme:', error);
      const systemTheme = getSystemTheme();
      applyTheme('system', systemTheme);
    }
  }
  
  // Execute immediately
  initializeTheme();
})();`;
