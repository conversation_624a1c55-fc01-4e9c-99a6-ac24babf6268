'use client';

import dynamic from 'next/dynamic';
import { ComponentType } from 'react';

/**
 * Higher-order component that disables SSR for a component
 * Useful for components that rely heavily on browser APIs
 */
export function withNoSSR<T extends object>(
  Component: ComponentType<T>
): ComponentType<T> {
  return dynamic(() => Promise.resolve(Component), { ssr: false });
}

/**
 * A component that only renders on the client-side
 * More robust than ClientOnly for complex components
 */
export const NoSSR = dynamic(
  () =>
    Promise.resolve(({ children }: { children: React.ReactNode }) => (
      <>{children}</>
    )),
  {
    ssr: false
  }
);
