'use client';

import { useTheme, type Theme } from '@/hooks';
import { createContext, useContext, type ReactNode } from 'react';

// Context types
interface AppContextType {
  theme: Theme;
  isDarkMode: boolean;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

// Create contexts
const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider component
interface AppProviderProps {
  children: ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  const themeState = useTheme();

  const contextValue: AppContextType = {
    ...themeState
  };

  // Don't render until hydrated to prevent theme-related hydration issues
  if (!themeState.isHydrated) {
    return (
      <AppContext.Provider value={contextValue}>
        <div suppressHydrationWarning>{children}</div>
      </AppContext.Provider>
    );
  }

  return (
    <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>
  );
}

// Custom hook to use the app context
export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}

// Re-export for convenience
export { useTheme };
export type { Theme };
