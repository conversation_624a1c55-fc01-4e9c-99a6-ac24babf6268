import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ImageWithFallback } from '../ImageWithFallback';

// Mock the cn utility
jest.mock('@/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' ')
}));

describe('ImageWithFallback', () => {
  const mockSrc = 'https://example.com/test-image.jpg';
  const mockAlt = 'Test image';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders image successfully when src loads', () => {
    render(<ImageWithFallback src={mockSrc} alt={mockAlt} />);
    
    const image = screen.getByRole('img');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', mockSrc);
    expect(image).toHaveAttribute('alt', mockAlt);
  });

  it('shows fallback when image fails to load', async () => {
    render(<ImageWithFallback src={mockSrc} alt={mockAlt} />);
    
    const image = screen.getByRole('img');
    
    // Simulate image load error
    fireEvent.error(image);
    
    await waitFor(() => {
      expect(screen.getByLabelText(mockAlt)).toBeInTheDocument();
    });
  });

  it('shows custom fallback content when provided', async () => {
    const customFallback = <div data-testid="custom-fallback">Custom fallback</div>;
    
    render(
      <ImageWithFallback 
        src={mockSrc} 
        alt={mockAlt} 
        fallback={customFallback}
      />
    );
    
    const image = screen.getByRole('img');
    fireEvent.error(image);
    
    await waitFor(() => {
      expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
    });
  });

  it('calls onError callback when image fails', async () => {
    const onError = jest.fn();
    
    render(
      <ImageWithFallback 
        src={mockSrc} 
        alt={mockAlt} 
        onError={onError}
      />
    );
    
    const image = screen.getByRole('img');
    fireEvent.error(image);
    
    expect(onError).toHaveBeenCalledTimes(1);
  });

  it('resets error state when src changes', async () => {
    const { rerender } = render(<ImageWithFallback src={mockSrc} alt={mockAlt} />);
    
    const image = screen.getByRole('img');
    fireEvent.error(image);
    
    await waitFor(() => {
      expect(screen.getByLabelText(mockAlt)).toBeInTheDocument();
    });
    
    // Change src
    const newSrc = 'https://example.com/new-image.jpg';
    rerender(<ImageWithFallback src={newSrc} alt={mockAlt} />);
    
    // Should show image again, not fallback
    expect(screen.getByRole('img')).toHaveAttribute('src', newSrc);
  });

  it('applies custom className and style', () => {
    const customClass = 'custom-class';
    const customStyle = { width: '100px', height: '100px' };
    
    render(
      <ImageWithFallback 
        src={mockSrc} 
        alt={mockAlt}
        className={customClass}
        style={customStyle}
      />
    );
    
    const image = screen.getByRole('img');
    expect(image).toHaveClass(customClass);
    expect(image).toHaveStyle(customStyle);
  });

  it('shows original URL in data attribute when showOriginalUrl is true', async () => {
    render(
      <ImageWithFallback 
        src={mockSrc} 
        alt={mockAlt}
        showOriginalUrl={true}
      />
    );
    
    const image = screen.getByRole('img');
    fireEvent.error(image);
    
    await waitFor(() => {
      const fallbackImg = screen.getByAltText('Image failed to load');
      expect(fallbackImg).toHaveAttribute('data-original-url', mockSrc);
    });
  });

  it('handles fallback image error gracefully', async () => {
    render(<ImageWithFallback src={mockSrc} alt={mockAlt} />);
    
    const image = screen.getByRole('img');
    fireEvent.error(image);
    
    await waitFor(() => {
      const fallbackImg = screen.getByAltText('Image failed to load');
      
      // Simulate fallback image error
      fireEvent.error(fallbackImg);
      
      // Should hide the fallback image
      expect(fallbackImg).toHaveStyle('display: none');
    });
  });

  it('forwards additional props to img element', () => {
    const dataTestId = 'test-image';
    
    render(
      <ImageWithFallback 
        src={mockSrc} 
        alt={mockAlt}
        data-testid={dataTestId}
        loading="lazy"
      />
    );
    
    const image = screen.getByTestId(dataTestId);
    expect(image).toHaveAttribute('loading', 'lazy');
  });
});
