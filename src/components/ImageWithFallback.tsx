'use client';

import { cn } from '@/utils';
import React, { useState } from 'react';

import Image from 'next/image';

const FALLBACK_IMAGE_SRC =
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgiIGhlaWdodD0iODgiIHZpZXdCb3g9IjAgMCA4OCA4OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMTYiIHk9IjE2IiB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHJ4PSI2IiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBvcGFjaXR5PSIwLjMiLz4KPHBhdGggZD0ibTE2IDU4IDE2LTE4IDMyIDMyIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBvcGFjaXR5PSIwLjMiLz4KPGNpcmNsZSBjeD0iNTMiIGN5PSIzNSIgcj0iNyIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgb3BhY2l0eT0iMC4zIi8+Cjwvc3ZnPgo=';

export interface ImageWithFallbackProps {
  /**
   * Image source URL
   */
  src: string;
  /**
   * Alt text for the image
   */
  alt: string;
  /**
   * Image width - required for Next.js Image optimization
   */
  width?: number;
  /**
   * Image height - required for Next.js Image optimization
   */
  height?: number;
  /**
   * CSS class name
   */
  className?: string;
  /**
   * Inline styles
   */
  style?: React.CSSProperties;
  /**
   * Custom fallback content to display when image fails to load
   * If not provided, uses default broken image icon
   */
  fallbackSrc?: string;
  /**
   * Image loading priority
   * @default false
   */
  priority?: boolean;
  /**
   * Image quality (1-100)
   * @default 75
   */
  quality?: number;
  /**
   * Image fill mode - makes image fill parent container
   * @default false
   */
  fill?: boolean;
  /**
   * Image sizes for responsive images
   */
  sizes?: string;
  /**
   * Placeholder behavior
   * @default 'empty'
   */
  placeholder?: 'blur' | 'empty';
  /**
   * Blur data URL for placeholder
   */
  blurDataURL?: string;
}

export function ImageWithFallback({
  src,
  alt,
  width,
  height,
  className,
  style,
  fallbackSrc = FALLBACK_IMAGE_SRC,
  priority = false,
  quality = 75,
  fill = false,
  sizes,
  placeholder = 'empty',
  blurDataURL
}: ImageWithFallbackProps) {
  const [hasError, setHasError] = useState(false);

  React.useEffect(() => {
    if (src) {
      setHasError(false);
    }
  }, [src]);

  if (hasError) {
    return (
      <div
        className={cn(
          'inline-block bg-gray-100 text-center align-middle',
          className
        )}
        style={style}
      >
        <div className='flex items-center justify-center w-full h-full'>
          <Image src={fallbackSrc} alt='Image failed to load' />
        </div>
      </div>
    );
  }

  return (
    <Image
      src={src}
      alt={alt}
      width={fill ? undefined : width}
      height={fill ? undefined : height}
      fill={fill}
      className={className}
      style={style}
      priority={priority}
      quality={quality}
      sizes={sizes}
      placeholder={placeholder}
      blurDataURL={blurDataURL}
      onError={() => setHasError(true)}
    />
  );
}
