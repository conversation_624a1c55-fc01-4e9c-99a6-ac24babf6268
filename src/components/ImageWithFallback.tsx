'use client';

import { cn } from '@/utils';
import React, { useCallback, useState } from 'react';

// Optimized SVG fallback image - smaller and more semantic
const FALLBACK_IMAGE_SRC =
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgiIGhlaWdodD0iODgiIHZpZXdCb3g9IjAgMCA4OCA4OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMTYiIHk9IjE2IiB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHJ4PSI2IiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBvcGFjaXR5PSIwLjMiLz4KPHBhdGggZD0ibTE2IDU4IDE2LTE4IDMyIDMyIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBvcGFjaXR5PSIwLjMiLz4KPGNpcmNsZSBjeD0iNTMiIGN5PSIzNSIgcj0iNyIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgb3BhY2l0eT0iMC4zIi8+Cjwvc3ZnPgo=';

export interface ImageWithFallbackProps
  extends React.ImgHTMLAttributes<HTMLImageElement> {
  /**
   * Custom fallback content to display when image fails to load
   * If not provided, uses default broken image icon
   */
  fallback?: React.ReactNode;
  /**
   * Custom fallback image source
   * If not provided, uses default SVG placeholder
   */
  fallbackSrc?: string;
  /**
   * Whether to show the original URL in data attribute for debugging
   * @default false
   */
  showOriginalUrl?: boolean;
  /**
   * Callback fired when image fails to load
   */
  onError?: (event: React.SyntheticEvent<HTMLImageElement, Event>) => void;
}

/**
 * Enhanced image component with robust fallback handling
 *
 * Features:
 * - Graceful fallback to placeholder when image fails to load
 * - Customizable fallback content and styling
 * - Proper accessibility with alt text handling
 * - TypeScript support with comprehensive prop types
 * - Performance optimized with useCallback
 * - Consistent styling with design system
 */
export function ImageWithFallback({
  src,
  alt,
  className,
  style,
  fallback,
  fallbackSrc = FALLBACK_IMAGE_SRC,
  showOriginalUrl = false,
  onError,
  ...rest
}: ImageWithFallbackProps) {
  const [hasError, setHasError] = useState(false);

  const handleError = useCallback(
    (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
      setHasError(true);
      onError?.(event);
    },
    [onError]
  );

  // Reset error state when src changes
  React.useEffect(() => {
    if (src) {
      setHasError(false);
    }
  }, [src]);

  // If image failed to load, show fallback
  if (hasError) {
    // Custom fallback content
    if (fallback) {
      return (
        <div
          className={cn('inline-flex items-center justify-center', className)}
          style={style}
          role='img'
          aria-label={alt || 'Image failed to load'}
        >
          {fallback}
        </div>
      );
    }

    // Default fallback with placeholder image
    return (
      <div
        className={cn(
          'inline-flex items-center justify-center',
          'bg-muted text-muted-foreground',
          'border border-border rounded-md',
          className
        )}
        style={style}
        role='img'
        aria-label={alt || 'Image failed to load'}
      >
        <img
          src={fallbackSrc}
          alt='Image failed to load'
          className='opacity-50'
          {...(showOriginalUrl && src && { 'data-original-url': src })}
          // Prevent infinite error loop on fallback image
          onError={(e) => {
            e.currentTarget.style.display = 'none';
          }}
        />
      </div>
    );
  }

  // Render normal image
  return (
    <img
      src={src}
      alt={alt}
      className={className}
      style={style}
      onError={handleError}
      {...rest}
    />
  );
}
