'use client';

import { cn } from '@/utils';
import Image from 'next/image';
import React, { useState } from 'react';

// Optimized SVG fallback image - smaller and more semantic
const FALLBACK_IMAGE_SRC =
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgiIGhlaWdodD0iODgiIHZpZXdCb3g9IjAgMCA4OCA4OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMTYiIHk9IjE2IiB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHJ4PSI2IiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBvcGFjaXR5PSIwLjMiLz4KPHBhdGggZD0ibTE2IDU4IDE2LTE4IDMyIDMyIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBvcGFjaXR5PSIwLjMiLz4KPGNpcmNsZSBjeD0iNTMiIGN5PSIzNSIgcj0iNyIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgb3BhY2l0eT0iMC4zIi8+Cjwvc3ZnPgo=';

export interface ImageWithFallbackProps {
  /**
   * Image source URL
   */
  src: string;
  /**
   * Alt text for the image
   */
  alt: string;
  /**
   * Image width - required for Next.js Image optimization
   */
  width?: number;
  /**
   * Image height - required for Next.js Image optimization
   */
  height?: number;
  /**
   * CSS class name
   */
  className?: string;
  /**
   * Inline styles
   */
  style?: React.CSSProperties;
  /**
   * Custom fallback content to display when image fails to load
   * If not provided, uses default broken image icon
   */
  fallbackSrc?: string;
  /**
   * Whether to show the original URL in data attribute for debugging
   * @default false
   */
  showOriginalUrl?: boolean;
  /**
   * Image loading priority
   * @default false
   */
  priority?: boolean;
  /**
   * Image quality (1-100)
   * @default 75
   */
  quality?: number;
  /**
   * Image fill mode - makes image fill parent container
   * @default false
   */
  fill?: boolean;
  /**
   * Image sizes for responsive images
   */
  sizes?: string;
  /**
   * Placeholder behavior
   * @default 'empty'
   */
  placeholder?: 'blur' | 'empty';
  /**
   * Blur data URL for placeholder
   */
  blurDataURL?: string;
}

export function ImageWithFallback({
  src,
  alt,
  width,
  height,
  className,
  style,
  fallbackSrc = FALLBACK_IMAGE_SRC,
  showOriginalUrl = false,
  priority = false,
  quality = 75,
  fill = false,
  sizes,
  placeholder = 'empty',
  blurDataURL
}: ImageWithFallbackProps) {
  const [hasError, setHasError] = useState(false);

  // Reset error state when src changes
  React.useEffect(() => {
    if (src) {
      setHasError(false);
    }
  }, [src]);

  // If image failed to load, show fallback
  if (hasError) {
    // Default fallback with placeholder image
    return (
      <div
        className={cn(
          'inline-flex items-center justify-center',
          'bg-muted text-muted-foreground',
          'border border-border rounded-md',
          className
        )}
        style={style}
        role='img'
        aria-label={alt || 'Image failed to load'}
      >
        {/* Use regular img for fallback SVG to avoid Next.js Image complexity */}
        <img
          src={fallbackSrc}
          alt='Image failed to load'
          className='opacity-50'
          {...(showOriginalUrl && src && { 'data-original-url': src })}
          // Prevent infinite error loop on fallback image
        />
      </div>
    );
  }

  // Render Next.js optimized image
  return (
    <Image
      src={src}
      alt={alt}
      width={fill ? undefined : width}
      height={fill ? undefined : height}
      fill={fill}
      className={className}
      style={style}
      priority={priority}
      quality={quality}
      sizes={sizes}
      placeholder={placeholder}
      blurDataURL={blurDataURL}
    />
  );
}
