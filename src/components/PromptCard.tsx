import React from 'react';
import { ChevronUp, ChevronDown, Co<PERSON>, Eye, Zap } from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { ImageWithFallback } from './figma/ImageWithFallback';

interface PromptCardProps {
  id: string;
  title: string;
  author: string;
  model: string;
  votes: number;
  thumbnail?: string;
  isPremium?: boolean;
  price?: number;
  isUpvoted?: boolean;
  onUpvote?: (id: string) => void;
  onDownvote?: (id: string) => void;
  onClick?: (id: string) => void;
}

export function PromptCard({
  id,
  title,
  author,
  model,
  votes,
  thumbnail,
  isPremium = false,
  price,
  isUpvoted = false,
  onUpvote,
  onDownvote,
  onClick
}: PromptCardProps) {
  return (
    <div 
      className="card-light rounded-xl p-6 hover:card-light cursor-pointer group theme-transition"
      onClick={() => onClick?.(id)}
    >
      <div className="flex gap-4">
        <div className="flex flex-col items-center space-y-2">
          <Button
            variant="ghost"
            size="sm"
            className={`p-1 h-8 w-8 ${isUpvoted ? 'text-purple-400' : 'text-muted-foreground'} hover:text-purple-400 transition-colors rounded-full`}
            onClick={(e) => {
              e.stopPropagation();
              onUpvote?.(id);
            }}
          >
            <ChevronUp className="w-4 h-4" />
          </Button>
          <span className={`text-sm font-medium ${votes > 0 ? 'text-green-500' : 'text-muted-foreground'}`}>
            {votes}
          </span>
          <Button
            variant="ghost"
            size="sm"
            className="p-1 h-8 w-8 text-muted-foreground hover:text-red-400 transition-colors rounded-full"
            onClick={(e) => {
              e.stopPropagation();
              onDownvote?.(id);
            }}
          >
            <ChevronDown className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="flex-1">
          {thumbnail && (
            <div className="w-full h-48 rounded-lg overflow-hidden mb-4 shadow-soft">
              <ImageWithFallback
                src={`https://images.unsplash.com/photo-${thumbnail}?w=400&h=300&fit=crop`}
                alt={title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>
          )}
          
          <div className="flex items-start justify-between mb-3">
            <h3 className="font-semibold text-lg group-hover:text-primary transition-colors">
              {title}
            </h3>
            {isPremium && (
              <div className="flex items-center space-x-2">
                <Badge className="gradient-purple-cyan text-white border-0 shadow-soft">
                  <Zap className="w-3 h-3 mr-1" />
                  Premium
                </Badge>
                {price && (
                  <span className="text-sm font-medium text-green-500">${price}</span>
                )}
              </div>
            )}
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-sm text-muted-foreground">by {author}</span>
              <Badge variant="secondary" className="glass border-light">
                {model}
              </Badge>
            </div>
            
            <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button variant="ghost" size="sm" className="p-2 rounded-full hover:bg-accent">
                <Eye className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="p-2 rounded-full hover:bg-accent">
                <Copy className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}