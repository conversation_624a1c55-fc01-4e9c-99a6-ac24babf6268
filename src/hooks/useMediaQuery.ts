'use client';

import { useEffect, useState } from 'react';

/**
 * Configuration options for the useIsMobile hook
 */
export interface UseIsMobileOptions {
  /**
   * The breakpoint width in pixels below which the device is considered mobile
   * @default 768
   */
  breakpoint?: number;
  /**
   * Initial value to use during SSR or before hydration
   * @default false
   */
  initialValue?: boolean;
}

/**
 * Custom hook to detect if the current device is mobile based on screen width
 *
 * This hook uses window.matchMedia for efficient media query matching and
 * handles SSR/hydration properly for Next.js applications.
 *
 * @param options Configuration options for the hook
 * @returns boolean indicating if the device is mobile
 */
export function useMediaQuery(options: UseIsMobileOptions = {}): boolean {
  const { breakpoint = 768, initialValue = false } = options;

  const [isMobile, setIsMobile] = useState<boolean>(initialValue);
  const [isHydrated, setIsHydrated] = useState<boolean>(false);

  useEffect(() => {
    // Mark as hydrated on client side
    setIsHydrated(true);

    // Create media query
    const mediaQuery = window.matchMedia(`(max-width: ${breakpoint - 1}px)`);

    // Set initial value based on current screen size
    setIsMobile(mediaQuery.matches);

    // Handler for media query changes
    const handleChange = (event: MediaQueryListEvent) => {
      setIsMobile(event.matches);
    };

    // Add listener for media query changes
    mediaQuery.addEventListener('change', handleChange);

    // Cleanup listener on unmount
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [breakpoint]);

  // Return initial value during SSR or before hydration
  if (!isHydrated) {
    return initialValue;
  }

  return isMobile;
}

/**
 * Hook variant that uses common breakpoints
 */
export const useIsMobile = () => useMediaQuery({ breakpoint: 768 });
export const useIsTablet = () => useMediaQuery({ breakpoint: 1024 });
export const useIsSmallScreen = () => useMediaQuery({ breakpoint: 640 });

export default useMediaQuery;
