'use client';

import { useCallback, useEffect, useLayoutEffect, useState } from 'react';

export type Theme = 'light' | 'dark' | 'system';

interface ThemeState {
  theme: Theme;
  resolvedTheme: 'light' | 'dark';
  isDarkMode: boolean;
  isLightMode: boolean;
  isHydrated: boolean;
}

interface ThemeActions {
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

type UseThemeReturn = ThemeState & ThemeActions;

// Constants
const STORAGE_KEY = 'theme';
const MEDIA_QUERY = '(prefers-color-scheme: dark)';

/**
 * Utilities for theme management
 */
const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light';
  return window.matchMedia(MEDIA_QUERY).matches ? 'dark' : 'light';
};

const getStoredTheme = (): Theme | null => {
  if (typeof window === 'undefined') return null;
  try {
    const stored = localStorage.getItem(STORAGE_KEY) as Theme | null;
    return stored && ['light', 'dark', 'system'].includes(stored)
      ? stored
      : null;
  } catch {
    return null;
  }
};

const resolveTheme = (theme: Theme): 'light' | 'dark' => {
  return theme === 'system' ? getSystemTheme() : theme;
};

const applyTheme = (resolvedTheme: 'light' | 'dark'): void => {
  const root = document.documentElement;
  const isDark = resolvedTheme === 'dark';

  root.classList.toggle('dark', isDark);
  root.style.colorScheme = resolvedTheme;
};

/**
 * Custom hook for managing theme state with persistence and system preference detection
 *
 * Features:
 * - SSR-safe theme loading
 * - System preference detection and following
 * - Persistent theme storage
 * - Smooth theme transitions without flash
 * - TypeScript support with proper types
 */
export function useTheme(): UseThemeReturn {
  // Initialize with system theme to match server rendering
  const [theme, setThemeState] = useState<Theme>('system');
  const [isHydrated, setIsHydrated] = useState(false);

  // Calculate resolved theme - use light as default for SSR
  const resolvedTheme = isHydrated ? resolveTheme(theme) : 'light';
  const isDarkMode = resolvedTheme === 'dark';
  const isLightMode = resolvedTheme === 'light';

  // Handle hydration and initial theme setup
  useEffect(() => {
    // Read theme from script attributes first, then localStorage
    const dataTheme = document.documentElement.getAttribute(
      'data-theme'
    ) as Theme;
    const storedTheme = getStoredTheme();
    const initialTheme = dataTheme || storedTheme || 'system';

    setThemeState(initialTheme);
    setIsHydrated(true);
  }, []);

  // Use useLayoutEffect to apply theme before paint to prevent flash
  useLayoutEffect(() => {
    if (isHydrated) {
      applyTheme(resolvedTheme);
    }
  }, [resolvedTheme, isHydrated]);

  // Listen for system theme changes
  useEffect(() => {
    if (theme !== 'system') return;

    const mediaQuery = window.matchMedia(MEDIA_QUERY);
    const handleChange = (e: MediaQueryListEvent) => {
      applyTheme(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);

  // Theme setter with persistence
  const setTheme = useCallback((newTheme: Theme) => {
    setThemeState(newTheme);

    try {
      if (newTheme === 'system') {
        localStorage.removeItem(STORAGE_KEY);
      } else {
        localStorage.setItem(STORAGE_KEY, newTheme);
      }
    } catch (error) {
      console.warn('Failed to save theme preference:', error);
    }
  }, []);

  // Theme toggle functionality
  const toggleTheme = useCallback(() => {
    if (theme === 'system') {
      // If system, toggle to opposite of current system preference
      setTheme(getSystemTheme() === 'dark' ? 'light' : 'dark');
    } else {
      // Toggle between light and dark
      setTheme(theme === 'light' ? 'dark' : 'light');
    }
  }, [theme, setTheme]);

  return {
    theme,
    resolvedTheme,
    isDarkMode,
    isLightMode,
    isHydrated,
    setTheme,
    toggleTheme
  };
}
