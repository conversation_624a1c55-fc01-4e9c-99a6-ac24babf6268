'use client';

import { PromptCard } from '@/components';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Clock, Filter, Search, TrendingUp } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function HomePage() {
  const router = useRouter();

  const [searchQuery, setSearchQuery] = useState('');
  const [votes, setVotes] = useState<Record<string, number>>({
    '1': 42,
    '2': 28,
    '3': 67,
    '4': 15,
    '5': 89,
    '6': 34
  });
  const [upvotedPrompts, setUpvotedPrompts] = useState<Set<string>>(new Set());

  const handleUpvote = (id: string) => {
    if (upvotedPrompts.has(id)) {
      setVotes((prev) => ({ ...prev, [id]: prev[id] - 1 }));
      setUpvotedPrompts((prev) => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    } else {
      setVotes((prev) => ({ ...prev, [id]: prev[id] + 1 }));
      setUpvotedPrompts((prev) => new Set([...prev, id]));
    }
  };

  const handleDownvote = (id: string) => {
    if (!upvotedPrompts.has(id)) {
      setVotes((prev) => ({ ...prev, [id]: prev[id] - 1 }));
    }
  };

  const trendingPrompts = [
    {
      id: '1',
      title: 'Create Stunning Product Photography',
      author: 'sarah_designs',
      model: 'Midjourney',
      votes: votes['1'],
      thumbnail: '1441974718637-9bc8fcbe5521',
      isPremium: true,
      price: 4.99,
      isUpvoted: upvotedPrompts.has('1')
    },
    {
      id: '2',
      title: 'Professional Email Templates',
      author: 'biz_writer',
      model: 'ChatGPT',
      votes: votes['2'],
      isUpvoted: upvotedPrompts.has('2')
    },
    {
      id: '3',
      title: 'Cinematic AI Video Prompts',
      author: 'film_creator',
      model: 'Sora',
      votes: votes['3'],
      thumbnail: '1511593358241-7de94017ccad',
      isPremium: true,
      price: 7.99,
      isUpvoted: upvotedPrompts.has('3')
    }
  ];

  const newestPrompts = [
    {
      id: '4',
      title: 'Minimalist Logo Design',
      author: 'designpro',
      model: 'Midjourney',
      votes: votes['4'],
      thumbnail: '1541462618373-e9ee10a56ff8',
      isUpvoted: upvotedPrompts.has('4')
    },
    {
      id: '5',
      title: 'Social Media Caption Generator',
      author: 'content_king',
      model: 'ChatGPT',
      votes: votes['5'],
      isPremium: true,
      price: 2.99,
      isUpvoted: upvotedPrompts.has('5')
    },
    {
      id: '6',
      title: 'Abstract Art Animations',
      author: 'art_wizard',
      model: 'Sora',
      votes: votes['6'],
      thumbnail: '1506905925323-ccce94d8e4d8',
      isUpvoted: upvotedPrompts.has('6')
    }
  ];

  const onPromptClick = (id: string) => {
    router.push(`/prompt/${id}`);
  };

  return (
    <div className='min-h-screen gradient-dark pt-24 pb-12'>
      <div className='max-w-7xl mx-auto px-6'>
        {/* Hero Section */}
        <div className='text-center mb-12'>
          <h1 className='text-4xl md:text-6xl font-bold mb-6'>
            Discover the Best <span className='text-gradient'>AI Prompts</span>
          </h1>
          <p className='text-xl text-muted-foreground mb-8 max-w-2xl mx-auto'>
            Find, vote, and sell high-quality prompts for ChatGPT, Midjourney,
            and Sora. Join the ultimate AI prompt marketplace.
          </p>

          {/* Search Bar */}
          <div className='max-w-2xl mx-auto relative'>
            <div className='glass rounded-full p-2 flex items-center'>
              <Search className='w-5 h-5 text-muted-foreground ml-4' />
              <Input
                placeholder='Search for prompts, creators, or AI models...'
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className='border-0 bg-transparent flex-1 px-4 focus:outline-none focus:ring-0'
              />
              <Button className='gradient-purple-cyan text-white border-0 rounded-full shadow-glow'>
                Search
              </Button>
            </div>
          </div>
        </div>

        {/* Quick Filters */}
        <div className='flex flex-wrap gap-3 mb-8 justify-center'>
          <Button
            variant='outline'
            className='glass hover:shadow-glow transition-all'
          >
            <Filter className='w-4 h-4 mr-2' />
            All Models
          </Button>
          <Button
            variant='outline'
            className='glass hover:shadow-glow transition-all'
          >
            ChatGPT
          </Button>
          <Button
            variant='outline'
            className='glass hover:shadow-glow transition-all'
          >
            Midjourney
          </Button>
          <Button
            variant='outline'
            className='glass hover:shadow-glow transition-all'
          >
            Sora
          </Button>
        </div>

        {/* Prompts Sections */}
        <Tabs defaultValue='trending' className='w-full'>
          <div className='flex justify-center mb-8'>
            <TabsList className='glass'>
              <TabsTrigger value='trending' className='flex items-center'>
                <TrendingUp className='w-4 h-4 mr-2' />
                Trending
              </TabsTrigger>
              <TabsTrigger value='newest' className='flex items-center'>
                <Clock className='w-4 h-4 mr-2' />
                Newest
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value='trending'>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
              {trendingPrompts.map((prompt) => (
                <PromptCard
                  key={prompt.id}
                  {...prompt}
                  onUpvote={handleUpvote}
                  onDownvote={handleDownvote}
                  onClick={onPromptClick}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value='newest'>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
              {newestPrompts.map((prompt) => (
                <PromptCard
                  key={prompt.id}
                  {...prompt}
                  onUpvote={handleUpvote}
                  onDownvote={handleDownvote}
                  onClick={onPromptClick}
                />
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
