'use client';

export default function UploadPage() {
  return (
    <main className='bg-background p-6'>
      <div className='max-w-7xl mx-auto'>
        <div className='text-center space-y-4'>
          <h1 className='text-4xl font-bold text-gradient'>Upload Prompt</h1>
          <p className='text-xl text-muted-foreground max-w-2xl mx-auto'>
            Share your best AI prompts with the community. Upload and contribute
            to the growing collection of effective prompts.
          </p>
          <div className='glass p-8 rounded-lg theme-transition max-w-2xl mx-auto mt-12'>
            <h3 className='text-lg font-semibold mb-4'>
              📤 Upload Your Prompt
            </h3>
            <p className='text-muted-foreground'>
              Upload form would go here. This demonstrates the navigation
              working with Next.js routing.
            </p>
          </div>
        </div>
      </div>
    </main>
  );
}
