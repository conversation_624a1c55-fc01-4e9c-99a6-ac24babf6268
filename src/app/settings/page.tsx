'use client';

export default function SettingsPage() {
  return (
    <main className='bg-background p-6'>
      <div className='max-w-7xl mx-auto'>
        <div className='text-center space-y-4'>
          <h1 className='text-4xl font-bold text-gradient'>Settings</h1>
          <p className='text-xl text-muted-foreground max-w-2xl mx-auto'>
            Manage your account preferences and settings.
          </p>
          <div className='glass p-8 rounded-lg theme-transition max-w-2xl mx-auto mt-12'>
            <h3 className='text-lg font-semibold mb-4'>⚙️ Account Settings</h3>
            <p className='text-muted-foreground'>
              Account settings interface would go here. This demonstrates the
              navigation working with Next.js routing.
            </p>
          </div>
        </div>
      </div>
    </main>
  );
}
