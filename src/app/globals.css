@import 'tailwindcss';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
  --font-size: 14px;
  /* Light Theme - Refined & Modern */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: #ffffff;
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --input-background: rgba(248, 250, 252, 0.9);
  --switch-background: #e2e8f0;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
}

.dark {
  /* Dark Theme - Futuristic & Premium */
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: #ffffff;
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-switch-background: var(--switch-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
}

@layer utilities {
  /* Enhanced theme-aware glassmorphism */
  .glass {
    @apply backdrop-blur-xl border;
    background: rgba(255, 255, 255, 0.7);
    border-color: rgba(148, 163, 184, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .dark .glass {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.3);
  }

  .glass-strong {
    @apply backdrop-blur-2xl border;
    background: rgba(255, 255, 255, 0.85);
    border-color: rgba(148, 163, 184, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.12);
  }

  .dark .glass-strong {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 2px 8px rgba(0, 0, 0, 0.4);
  }

  /* Enhanced card styling for light mode */
  .card-light {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(148, 163, 184, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.08);
  }

  .card-light:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(148, 163, 184, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 3px 10px rgba(0, 0, 0, 0.12);
  }

  .dark .card-light {
    background: rgba(15, 15, 25, 0.85);
    border: 1px solid rgba(75, 85, 99, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.3);
  }

  .dark .card-light:hover {
    background: rgba(15, 15, 25, 0.95);
    border-color: rgba(75, 85, 99, 0.4);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 3px 10px rgba(0, 0, 0, 0.4);
  }

  /* Theme-aware gradients */
  .gradient-purple-cyan {
    background: linear-gradient(135deg, #6366f1 0%, #06b6d4 100%);
  }

  .dark .gradient-purple-cyan {
    background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
  }

  .gradient-dark {
    background: linear-gradient(
      135deg,
      #f1f5f9 0%,
      #e2e8f0 30%,
      #cbd5e1 70%,
      #94a3b8 100%
    );
  }

  .dark .gradient-dark {
    background: linear-gradient(135deg, #0f0f19 0%, #1e1e2d 50%, #2d2d45 100%);
  }

  .gradient-card {
    background: linear-gradient(
      135deg,
      rgba(99, 102, 241, 0.08) 0%,
      rgba(6, 182, 212, 0.08) 100%
    );
  }

  .dark .gradient-card {
    background: linear-gradient(
      135deg,
      rgba(139, 92, 246, 0.1) 0%,
      rgba(6, 182, 212, 0.1) 100%
    );
  }

  /* Enhanced background pattern for light mode */
  .bg-pattern-light {
    background-image: radial-gradient(
      circle at 1px 1px,
      rgba(148, 163, 184, 0.15) 1px,
      transparent 0
    );
    background-size: 20px 20px;
  }

  .dark .bg-pattern-light {
    background-image: radial-gradient(
      circle at 1px 1px,
      rgba(75, 85, 99, 0.2) 1px,
      transparent 0
    );
    background-size: 20px 20px;
  }

  /* Theme-aware text gradients */
  .text-gradient {
    @apply bg-gradient-to-r from-indigo-600 to-cyan-600 bg-clip-text text-transparent;
  }

  .dark .text-gradient {
    @apply bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent;
  }

  /* Enhanced theme-aware glows */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.25),
      0 0 40px rgba(99, 102, 241, 0.1);
  }

  .dark .shadow-glow {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }

  .shadow-glow-cyan {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.25),
      0 0 40px rgba(6, 182, 212, 0.1);
  }

  .dark .shadow-glow-cyan {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
  }

  /* Light theme specific shadows */
  .shadow-soft {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.08);
  }

  .shadow-medium {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06), 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  .shadow-strong {
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1), 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .dark .shadow-soft,
  .dark .shadow-medium,
  .dark .shadow-strong {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3), 0 2px 8px rgba(0, 0, 0, 0.4);
  }

  /* Enhanced light theme gradient */
  .gradient-light {
    background: linear-gradient(
      135deg,
      #ffffff 0%,
      #f8fafc 25%,
      #f1f5f9 75%,
      #e2e8f0 100%
    );
  }

  .dark .gradient-light {
    background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  }

  /* Smooth transitions for theme switching */
  .theme-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Enhanced light theme button styles */
  .btn-light-enhanced {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(148, 163, 184, 0.25);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .btn-light-enhanced:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(148, 163, 184, 0.35);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }

  .dark .btn-light-enhanced {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.5);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }

  .dark .btn-light-enhanced:hover {
    background: rgba(30, 41, 59, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }

  /* Light mode specific border styles */
  .border-light {
    border-color: rgba(148, 163, 184, 0.2);
  }

  .border-light-strong {
    border-color: rgba(148, 163, 184, 0.3);
  }

  .dark .border-light {
    border-color: rgba(75, 85, 99, 0.3);
  }

  .dark .border-light-strong {
    border-color: rgba(75, 85, 99, 0.4);
  }
}

/**
 * Base typography. This is not applied to elements which have an ancestor with a Tailwind text class.
 */
@layer base {
  :where(:not(:has([class*=' text-']), :not(:has([class^='text-'])))) {
    h1 {
      font-size: var(--text-2xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h2 {
      font-size: var(--text-xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h3 {
      font-size: var(--text-lg);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h4 {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    p {
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }

    label {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    button {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    input {
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }
  }
}

html {
  font-size: var(--font-size);
}
