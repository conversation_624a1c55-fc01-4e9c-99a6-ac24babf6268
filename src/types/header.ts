import { ScreenRoute } from '@/enums';
import { UserInfo } from '@/types';

export interface NavigationItem {
  id: ScreenRoute;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  path: string;
}

export interface DesktopNavigationProps {
  currentScreen: ScreenRoute; // Ensure this matches ScreenRoute
  onNavigate: (screen: ScreenRoute) => void;
  isDarkMode: boolean;
  onThemeToggle: () => void;
}

export interface MobileMenuProps {
  isOpen: boolean;
  currentScreen: ScreenRoute;
  isDarkMode: boolean;
  userInfo: UserInfo;
  onOpenChange: (open: boolean) => void;
  onNavigate: (screen: ScreenRoute) => void;
  onThemeToggle: () => void;
}

export interface LogoProps {
  onClick: () => void;
}

export interface ThemeToggleProps {
  isDarkMode: boolean;
  isMobile?: boolean;
  onToggle: () => void;
}

export interface NavigationButtonProps {
  item: NavigationItem;
  isActive: boolean;
  isMobile?: boolean;
  onClick: () => void;
}
